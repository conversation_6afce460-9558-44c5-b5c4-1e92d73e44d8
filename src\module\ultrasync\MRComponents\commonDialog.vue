<template>
    <van-dialog v-model="show" v-bind="$attrs" :class="['common_dialog', { 'confirm-disabled': confirmButtonDisabled }]" :show-cancel-button="showRejectButton"
        :show-confirm-button="showConfirmButton" :confirm-button-text="confirmButtonText || lang.confirm_txt"
        :cancel-button-text="rejectButtonText || lang.cancel_btn" :message-align="messageAlign"
        :before-close="handleBeforeClose" @confirm="handleConfirm" @cancel="handleReject"
        :closeOnClickOverlay="closeOnClickOverlay" :close-on-popstate="false">
        <div class="common_dialog-title">{{ title || lang.tip_title }}</div>
        <div class="common_dialog-content" :class="[`align_${messageAlign}`]">
            <slot>
                <p v-html="message" class="common_dialog-message"></p>
            </slot>
        </div>
        <div class="cancel_button" v-if="showCancelButton" @click="handelCancelButton">
            <van-icon name="close" size="24" color="#f54040" />
        </div>
    </van-dialog>
    <!-- <div style="position:fixed;top:0;bottom:0;left:0;right:0;background: #000;" v-if="show"></div> -->
</template>
<script>
import { Dialog, Icon } from "vant";
import Tool from '@/common/tool'
export default {
    model: {
        prop: "value",
        event: "change",
    },
    name: "CommonDialog",
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        showConfirmButton: {
            type: Boolean,
            default: true
        },
        showCancelButton: {
            type: Boolean,
            default: true
        },
        showRejectButton: {
            type: Boolean,
            default: false,
        },
        closeOnClickOverlay: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: ''
        },
        confirmButtonText: {
            type: String,
            default: ''
        },
        rejectButtonText: {
            type: String,
            default: ''
        },
        messageAlign: {
            type: String,
            default: 'center'
        },
        message: {
            type: String,
            default: ''
        },
        closeOnPopstate: {
            type: Boolean,
            default: true
        },
        forbiddenClose: {
            type: Boolean,
            default: false
        },
        confirmButtonDisabled: {
            type: Boolean,
            default: false
        }

    },
    components: {
        [Dialog.Component.name]: Dialog.Component,
        VanIcon: Icon,
    },
    computed: {
        lang() {
            return this.$store.state.language
        }
    },
    watch: {
        value: {
            handler(val) {
                this.show = val;
                if (val) {
                    this.currentDialogId = Tool.genID(3)
                    this.$root.currentDialogList.push({
                        id: this.currentDialogId,
                        el: this
                    })
                } else {
                    this.$root.currentDialogList = this.$root.currentDialogList.filter(item => item.id !== this.currentDialogId)
                }
            }
        },
        show: {
            handler(val) {
                this.$emit("change", val);
            },
        },
    },
    data() {
        return {
            show: false,
            currentDialogId: 0
        };
    },
    created() {

    },
    mounted() {


    },
    methods: {
        showDialog() {
            this.show = true
        },
        handleBeforeClose(action, done) {
            // 如果是确认操作且按钮被禁用，直接阻止
            if (action === 'confirm' && this.confirmButtonDisabled) {
                done(false);
                return;
            }

            // 如果有自定义的 beforeClose 处理函数，则调用它
            if (this.$listeners.beforeClose) {
                this.$emit('beforeClose', action, done);
            } else {
                // 默认行为：直接关闭
                done();
            }
        },
        handleConfirm() {
            // 如果确认按钮被禁用，则不触发确认事件
            if (this.confirmButtonDisabled) {
                return;
            }
            this.$emit('confirm')
        },
        handleReject() {
            this.$emit('reject')
        },
        handelCancelButton() {
            this.closeDialog()
            this.$emit('cancel')
        },
        closeDialog() {
            this.show = false;
        },
        checkCanClose() {
            return !this.forbiddenClose
        },
        checkCanCloseOnPopstate() {
            return this.closeOnPopstate && !this.forbiddenClose
        },
        checkIsShow() {
            return this.show
        }
    },
};
</script>
<style lang="scss">
.common_dialog {
    min-width: 300px;
    width: 80%;
    max-width: 540px;
    border-radius: 5px;

    .common_dialog-title {
        font-size: 0.82rem; // 18px = 0.82rem
        font-weight: 600;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 2.45rem; // 54px = 2.45rem
        line-height: 1.4;
        padding: 0.55rem 0.91rem 0.36rem 0.91rem; // 12px 20px 8px 20px
        text-align: center;
        word-break: break-word;
        white-space: normal;
    }

    .common_dialog-content {
        min-height: 2rem; // 44px = 2rem
        padding: 0.55rem 1rem; // 12px 22px
        font-size: 0.73rem; // 16px = 0.73rem

        .common_dialog-message {
            word-break: break-word;
        }
    }


    .cancel_button {
        position: absolute;
        top: 0;
        z-index: 10;
        right: 0;
    }

    .align_left {
        text-align: left;
    }

    .align_center {
        text-align: center;
    }

    .align_right {
        text-align: right;
    }

    .van-checkbox-group {
        position: relative !important;
        border-radius: 0rem !important;

        .van-checkbox {
            padding: 0px !important;
        }
    }
    .van-dialog__footer {
        .van-button {
            color: #00c59d;
        }
    }

    // 禁用确认按钮的样式
    &.confirm-disabled {
        .van-dialog__footer {
            .van-button--default:last-child {
                background-color: #f5f5f5 !important;
                color: #c8c9cc !important;
                border-color: #f5f5f5 !important;
                cursor: not-allowed !important;
                opacity: 0.6;
                // pointer-events: none !important; // 不使用这个，让我们的逻辑处理

                &:active,
                &:hover,
                &:focus {
                    background-color: #f5f5f5 !important;
                    color: #c8c9cc !important;
                    border-color: #f5f5f5 !important;
                    transform: none !important; // 禁用任何变换效果
                    box-shadow: none !important; // 禁用阴影效果
                }

                // 禁用 Vant 按钮的点击特效和涟漪效果
                &::before,
                &::after {
                    display: none !important;
                }

                // 禁用可能的涟漪效果
                .van-button__loading,
                .van-button__text {
                    pointer-events: none !important;
                }

                // 禁用触摸反馈
                -webkit-tap-highlight-color: transparent !important;
                -webkit-touch-callout: none !important;
                -webkit-user-select: none !important;
                -moz-user-select: none !important;
                -ms-user-select: none !important;
                user-select: none !important;
            }
        }
    }

}
</style>
