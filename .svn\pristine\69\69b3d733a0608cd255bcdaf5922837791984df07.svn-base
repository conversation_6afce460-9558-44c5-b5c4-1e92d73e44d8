<template>
    <div>
        <commonDialog
            :value="visible"
            @input="updateVisible"
            :title="lang.improve_information"
            :showConfirmButton="true"
            :showRejectButton="false"
            :showCancelButton="false"
            :confirmButtonText="lang.confirm_txt"
            :forbiddenClose="true"
            :closeOnClickOverlay="false"
            :closeOnPopstate="false"
            :confirmButtonDisabled="isConfirmButtonDisabled"
            @beforeClose="handleBeforeClose"
            class="professional_identity_dialog"
        >
            <div class="dialog-content">
                <van-form ref="form" class="custom-form">
                    <!-- 应用领域 -->
                    <div class="form-item">
                        <div class="form-label">
                            <span class="required-mark">*</span>
                            {{ lang.application_area }}
                        </div>
                        <van-field
                            v-model="applicationAreaLabel"
                            name="application_area"
                            :placeholder="lang.input_select_tips"
                            readonly
                            is-link
                            :rules="[{ required: true, message: lang.input_select_tips }]"
                            @click="showApplicationAreaPicker = true"
                            class="form-field-input"
                        />
                    </div>

                    <!-- 职业身份 -->
                    <div class="form-item">
                        <div class="form-label">
                            <span v-if="!isProfessionalIdentityDisabled" class="required-mark">*</span>
                            {{ lang.professional_identity_title }}
                        </div>
                        <van-field
                            v-model="professionalIdentityLabel"
                            name="professional_identity"
                            :placeholder="lang.input_select_tips"
                            readonly
                            is-link
                            :rules="isProfessionalIdentityDisabled ? [] : [{ required: true, message: lang.input_select_tips }]"
                            :disabled="isProfessionalIdentityDisabled"
                            @click="!isProfessionalIdentityDisabled && (showProfessionalIdentityPicker = true)"
                            class="form-field-input"
                        />
                    </div>

                    <!-- 备注 - 仅在选择需要备注的职业身份时显示 -->
                    <div v-if="showRemarkField" class="form-item">
                        <div class="form-label">
                            <span class="required-mark">*</span>
                            {{ lang.remark_text }}
                        </div>
                        <van-field
                            v-model="formData.professional_identity_remark"
                            name="professional_identity_remark"
                            :placeholder="lang.please_enter_detailed_professional_identity"
                            :rules="[{ required: true, message: lang.please_enter_detailed_professional_identity }]"
                            class="form-field-input"
                        />
                    </div>

                    <!-- 机构信息 -->
                    <div class="form-item">
                        <div class="form-label">
                            <span v-if="!isFormDisabled" class="required-mark">*</span>
                            {{ lang.organization_name }}
                        </div>
                        <van-field
                            v-model="formData.organization"
                            name="organization"
                            :placeholder="lang.input_enter_tips"
                            :rules="isFormDisabled ? [] : [{ required: true, message: lang.input_enter_tips }]"
                            :disabled="isFormDisabled"
                            class="form-field-input"
                        />
                    </div>

                    <!-- 其他应用领域的提示信息 -->
                    <div v-if="showOtherAreaTip" class="other-area-tip">
                        {{ lang.other_application_area_tip }}
                    </div>
                </van-form>

                <!-- 应用领域选择器 -->
                <van-popup v-model="showApplicationAreaPicker" position="bottom">
                    <van-picker
                        :columns="applicationAreaOptions"
                        @confirm="onApplicationAreaConfirm"
                        @cancel="showApplicationAreaPicker = false"
                        show-toolbar
                        :title="lang.application_area"
                    />
                </van-popup>

                <!-- 职业身份选择器 -->
                <van-popup v-model="showProfessionalIdentityPicker" position="bottom">
                    <van-picker
                        :columns="currentIdentityOptions"
                        @confirm="onProfessionalIdentityConfirm"
                        @cancel="showProfessionalIdentityPicker = false"
                        show-toolbar
                        :title="lang.professional_identity_title"
                    />
                </van-popup>
            </div>
        </commonDialog>
    </div>
</template>

<script>
import base from "../lib/base";
import commonDialog from "../MRComponents/commonDialog.vue";
import { PROFESSIONAL_IDENTITY, APPLICATION_AREA, PROFESSIONAL_IDENTITY_BY_APPLICATION_AREA } from "../lib/constants.js";
import service from "../service/service";
import { Field, Popup, Picker, Toast, Form } from "vant";

export default {
    mixins: [base],
    name: "ProfessionalIdentityDialog",
    components: {
        commonDialog,
        [Field.name]: Field,
        [Popup.name]: Popup,
        [Picker.name]: Picker,
        [Form.name]: Form,
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            visible: this.show,
            formData: {
                application_area: null,
                professional_identity: null,
                professional_identity_remark: '',
                organization: ''
            },
            applicationAreaLabel: '',
            professionalIdentityLabel: '',
            showApplicationAreaPicker: false,
            showProfessionalIdentityPicker: false,
            applicationAreaOptions: [],
            allIdentityOptions: {}
        };
    },
    created() {
        // 初始化应用领域选项
        this.applicationAreaOptions = [
            { text: this.lang.medical_institution, value: APPLICATION_AREA.MEDICAL_INSTITUTION },
            { text: this.lang.medical_school, value: APPLICATION_AREA.MEDICAL_SCHOOL },
            { text: this.lang.vendor_staff, value: APPLICATION_AREA.VENDOR_PERSONNEL },
            { text: this.lang.professional_identity.other, value: APPLICATION_AREA.OTHER }
        ];

        // 初始化职业身份选项
        this.allIdentityOptions = {
            [PROFESSIONAL_IDENTITY.PHYSICIAN]: { text: this.lang.professional_identity.physician, value: PROFESSIONAL_IDENTITY.PHYSICIAN },
            [PROFESSIONAL_IDENTITY.SONOGRAPHER]: { text: this.lang.professional_identity.sonographer, value: PROFESSIONAL_IDENTITY.SONOGRAPHER },
            [PROFESSIONAL_IDENTITY.RESEARCHER]: { text: this.lang.professional_identity.researcher, value: PROFESSIONAL_IDENTITY.RESEARCHER },
            [PROFESSIONAL_IDENTITY.OTHER_MEDICAL]: { text: this.lang.professional_identity.other_users_related_medical_field, value: PROFESSIONAL_IDENTITY.OTHER_MEDICAL },
            [PROFESSIONAL_IDENTITY.ADMINISTRATOR]: { text: this.lang.professional_identity.administrator, value: PROFESSIONAL_IDENTITY.ADMINISTRATOR },
            [PROFESSIONAL_IDENTITY.TECHNICAL_SUPPORT]: { text: this.lang.professional_identity.technical_support, value: PROFESSIONAL_IDENTITY.TECHNICAL_SUPPORT },
            [PROFESSIONAL_IDENTITY.OTHER_VENDOR]: { text: this.lang.professional_identity.other, value: PROFESSIONAL_IDENTITY.OTHER_VENDOR },
            [PROFESSIONAL_IDENTITY.TEACHER]: { text: this.lang.teacher, value: PROFESSIONAL_IDENTITY.TEACHER },
            [PROFESSIONAL_IDENTITY.STUDENT]: { text: this.lang.student, value: PROFESSIONAL_IDENTITY.STUDENT },
            [PROFESSIONAL_IDENTITY.OTHER_MEDICAL_SCHOOL]: { text: this.lang.professional_identity.other_medical_school, value: PROFESSIONAL_IDENTITY.OTHER_MEDICAL_SCHOOL }
        };
    },
    computed: {
        // 当前可选的职业身份选项
        currentIdentityOptions() {
            if (!this.formData.application_area) {
                return [];
            }

            const availableIdentities = PROFESSIONAL_IDENTITY_BY_APPLICATION_AREA[this.formData.application_area] || [];
            return availableIdentities.map(identityId => this.allIdentityOptions[identityId]);
        },

        // 是否显示备注字段
        showRemarkField() {
            return this.formData.professional_identity === PROFESSIONAL_IDENTITY.OTHER_MEDICAL ||
                   this.formData.professional_identity === PROFESSIONAL_IDENTITY.OTHER_MEDICAL_SCHOOL;
        },

        // 是否显示"其他"应用领域的提示
        showOtherAreaTip() {
            return this.formData.application_area === APPLICATION_AREA.OTHER;
        },

        // 表单是否禁用
        isFormDisabled() {
            return this.formData.application_area === APPLICATION_AREA.OTHER;
        },

        // 职业身份选择框是否禁用
        isProfessionalIdentityDisabled() {
            return this.isFormDisabled || !this.formData.application_area;
        },

        // 确认按钮是否禁用
        isConfirmButtonDisabled() {
            return this.formData.application_area === APPLICATION_AREA.OTHER;
        }
    },
    watch: {
        show(newVal) {
            this.visible = newVal;
            if (newVal) {
                this.formData.organization = this.user.organizationName || '';
            }
        },
        visible(newVal) {
            this.$emit("update:show", newVal);
        },
        // 监听路由变化
        $route() {
            // 如果路由name不再是index，则自动关闭弹窗
            if (this.visible) {
                this.visible = false;
            }
        },
    },
    methods: {
        updateVisible(val) {
            this.visible = val;
        },

        // 应用领域选择确认
        onApplicationAreaConfirm(value) {
            this.formData.application_area = value.value;
            this.applicationAreaLabel = value.text;
            this.showApplicationAreaPicker = false;

            // 清空职业身份选择
            this.formData.professional_identity = null;
            this.professionalIdentityLabel = '';
            this.formData.professional_identity_remark = '';

            // 如果选择了"其他"，清空机构信息；否则恢复默认值
            if (this.formData.application_area === APPLICATION_AREA.OTHER) {
                this.formData.organization = '';
            } else {
                // 恢复默认的机构信息
                this.formData.organization = this.user.organizationName || '';
            }
        },

        // 职业身份选择确认
        onProfessionalIdentityConfirm(value) {
            this.formData.professional_identity = value.value;
            this.professionalIdentityLabel = value.text;
            this.showProfessionalIdentityPicker = false;

            // 如果不是需要备注的选项，清空备注
            if (this.formData.professional_identity !== PROFESSIONAL_IDENTITY.OTHER_MEDICAL &&
                this.formData.professional_identity !== PROFESSIONAL_IDENTITY.OTHER_MEDICAL_SCHOOL) {
                this.formData.professional_identity_remark = '';
            }
        },

        handleBeforeClose(action, done) {
            // 只有点击确认按钮时才进行验证和提交
            if (action === "confirm") {
                // 如果选择了"其他"应用领域，不允许提交
                if (this.formData.application_area === APPLICATION_AREA.OTHER) {
                    done(false);
                    return;
                }

                // 验证表单
                this.$refs.form.validate().then(() => {
                    // 验证通过，调用API
                    this.setProfessionalIdentity(done);
                }).catch(() => {
                    // 验证失败
                    done(false);
                });
            } else {
                // 其他情况（如点击取消）直接关闭
                done();
            }
        },

        setProfessionalIdentity(done) {
            // 构建提交数据
            const submitData = {
                application_area: this.formData.application_area,
                professional_identity: this.formData.professional_identity,
                organization: this.formData.organization,
                professional_identity_remark: ''
            };

            // 如果选择了需要备注的选项，添加备注
            if (this.formData.professional_identity === PROFESSIONAL_IDENTITY.OTHER_MEDICAL ||
                this.formData.professional_identity === PROFESSIONAL_IDENTITY.OTHER_MEDICAL_SCHOOL) {
                submitData.professional_identity_remark = this.formData.professional_identity_remark;
            }

            service.setProfessionalIdentity(submitData).then((res) => {
                console.log("setProfessionalIdentity:", res);
                if (res.data && res.data.error_code === 0) {
                    // 更新用户信息
                    this.$store.commit("user/updateUser", submitData);
                    this.$emit("success");
                    Toast(this.lang.operate_success);
                } else {
                    Toast(this.lang.operate_err);
                }
                done(); // 不管成功还是失败都关闭弹窗
            }).catch((error) => {
                console.error("设置职业身份失败:", error);
                Toast(this.lang.operate_err);
                done(); // 出错时也关闭弹窗
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.professional_identity_dialog {
    .dialog-content {
        .custom-form {
            .form-item {
                margin-bottom: 0.91rem; // 20px = 0.91rem
                .form-label {
                    margin-bottom: 0.36rem; // 8px = 0.36rem
                    font-size: 0.64rem; // 14px = 0.64rem
                    color: #323233;
                    font-weight: 500;
                    text-align: left; // 确保label靠左对齐

                    .required-mark {
                        color: #ee0a24;
                        margin-right: 0.18rem; // 4px = 0.18rem
                    }
                }

                .form-field-input {
                    background: #f7f8fa;
                    border-radius: 0.27rem; // 6px = 0.27rem

                    &.van-field--disabled {
                        background: #f5f5f5;
                        color: #c8c9cc;
                    }

                    // 移除默认的label样式，因为我们使用自定义label
                    :deep(.van-field__label) {
                        display: none;
                    }
                }
            }

            .other-area-tip {
                padding: 0.55rem; // 12px = 0.55rem
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 0.18rem; // 4px = 0.18rem
                color: #856404;
                font-size: 0.64rem; // 14px = 0.64rem
                line-height: 1.5;
            }
        }
    }
}

// 全局样式，确保placeholder颜色不受任何状态影响
:deep(.professional_identity_dialog) {
    .van-field__control input::placeholder,
    .van-field__control textarea::placeholder {
        color: #c8c9cc !important;
    }

    // 针对错误状态的特殊处理
    .van-field--error .van-field__control input::placeholder,
    .van-field--error .van-field__control textarea::placeholder {
        color: #c8c9cc !important;
    }

    // 针对Vant可能添加的其他状态类
    .van-field.van-field--error input::placeholder,
    .van-field.van-field--error textarea::placeholder {
        color: #c8c9cc !important;
    }

    // 更具体的选择器，覆盖所有可能的情况
    .custom-form .form-field-input input::placeholder,
    .custom-form .form-field-input textarea::placeholder {
        color: #c8c9cc !important;
    }

    .custom-form .form-field-input.van-field--error input::placeholder,
    .custom-form .form-field-input.van-field--error textarea::placeholder {
        color: #c8c9cc !important;
    }
}
</style>
