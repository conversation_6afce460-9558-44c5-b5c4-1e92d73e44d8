<template>
    <div>
        <CommonDialog
            :title="lang.improve_information"
            :show.sync="visible"
            width="500px"
            height="auto"
            @submit="submit"
            @handleClickDialogClose="handleClickDialogClose"
            :submitText="lang.confirm_txt"
            :closeOnClickModal="false"
            :closeOnPressEscape="false"
            :footShow="true"
            :headerShow="true"
            :showCloseButton="false"
            :disabledSubmit="isFormDisabled"
            append-to-body
            class="professional_identity_dialog"
            @click.native.stop="handleClickDialog"
        >
            <div class="dialog-content">
                <el-form
                    ref="form"
                    :model="formData"
                    :rules="formRules"
                    label-width="auto"
                    label-position="top"
                    size="medium"
                    class="vertical-form"
                >
                    <!-- 应用领域 -->
                    <el-form-item :label="lang.application_area" prop="application_area" required>
                        <el-select
                            v-model="formData.application_area"
                            :placeholder="lang.input_select_tips"
                            style="width: 100%"
                            @change="onApplicationAreaChange"
                        >
                            <el-option
                                v-for="item in applicationAreaOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <!-- 职业身份 -->
                    <el-form-item
                        :label="lang.professional_identity_title"
                        :prop="isProfessionalIdentityDisabled ? '' : 'professional_identity'"
                        :required="!isProfessionalIdentityDisabled"
                    >
                        <el-select
                            v-model="formData.professional_identity"
                            :placeholder="lang.input_select_tips"
                            style="width: 100%"
                            :disabled="isProfessionalIdentityDisabled"
                            @change="onProfessionalIdentityChange"
                        >
                            <el-option
                                v-for="item in currentIdentityOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <!-- 备注 - 仅在选择"其他医疗领域相关用户"时显示 -->
                    <el-form-item
                        v-if="showRemarkField"
                        :label="lang.remark_text"
                        prop="professional_identity_remark"
                        required
                    >
                        <el-input
                            v-model="formData.professional_identity_remark"
                            :placeholder="lang.please_enter_detailed_professional_identity"
                            style="width: 100%"
                        />
                    </el-form-item>

                    <!-- 机构信息 -->
                    <el-form-item
                        :label="lang.organization_name"
                        prop="organization"
                        :required="!isFormDisabled"
                    >
                        <el-input
                            v-model="formData.organization"
                            :placeholder="lang.input_enter_tips"
                            style="width: 100%"
                            :disabled="isFormDisabled"
                            @focu
                        />
                    </el-form-item>

                    <!-- 其他应用领域的提示信息 -->
                    <div v-if="showOtherAreaTip" class="other-area-tip">
                        {{ lang.other_application_area_tip }}
                    </div>
                </el-form>
            </div>
        </CommonDialog>
    </div>
</template>

<script>
import base from "../lib/base";
import CommonDialog from "../MRComponents/commonDialog.vue";
import { PROFESSIONAL_IDENTITY, APPLICATION_AREA, PROFESSIONAL_IDENTITY_BY_APPLICATION_AREA } from "../lib/constants.js";
import service from "../service/service";
import Tool from "@/common/tool";

export default {
    mixins: [base],
    name: "ProfessionalIdentityDialog",
    components: {
        CommonDialog
    },
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            visible: this.show,
            formData: {
                application_area: null,
                professional_identity: null,
                professional_identity_remark: '',
                organization:''
            },
            applicationAreaOptions: [

            ],
            allIdentityOptions: {

            },
            formRules: {

            }
        };
    },
    computed: {
        // 当前可选的职业身份选项
        currentIdentityOptions() {
            if (!this.formData.application_area) {
                return [];
            }

            const availableIdentities = PROFESSIONAL_IDENTITY_BY_APPLICATION_AREA[this.formData.application_area] || [];
            return availableIdentities.map(identityId => this.allIdentityOptions[identityId]);
        },

        // 是否显示备注字段
        showRemarkField() {
            return this.formData.professional_identity === PROFESSIONAL_IDENTITY.OTHER_MEDICAL ||
                   this.formData.professional_identity === PROFESSIONAL_IDENTITY.OTHER_MEDICAL_SCHOOL;
        },

        // 是否显示"其他"应用领域的提示
        showOtherAreaTip() {
            return this.formData.application_area === APPLICATION_AREA.OTHER;
        },

        // 表单是否禁用
        isFormDisabled() {
            return this.formData.application_area === APPLICATION_AREA.OTHER;
        },

        // 职业身份选择框是否禁用
        isProfessionalIdentityDisabled() {
            return this.isFormDisabled || !this.formData.application_area;
        }
    },
    watch: {
        show(newVal) {
            this.visible = newVal;
            if (newVal) {
                this.formData.organization = this.user.organizationName || '';
            }
        },
        visible(newVal) {
            this.$emit('update:show', newVal);
        },
        // 监听路由变化
        '$route'() {
            // 如果路由name不再是index，则自动关闭弹窗
            if (this.visible) {
                this.visible = false;
            }
        }
    },
    created() {
        this.allIdentityOptions = {
            [PROFESSIONAL_IDENTITY.PHYSICIAN]: { value: PROFESSIONAL_IDENTITY.PHYSICIAN, label: this.lang.professional_identity.physician },
            [PROFESSIONAL_IDENTITY.SONOGRAPHER]: { value: PROFESSIONAL_IDENTITY.SONOGRAPHER, label: this.lang.professional_identity.sonographer },
            [PROFESSIONAL_IDENTITY.RESEARCHER]: { value: PROFESSIONAL_IDENTITY.RESEARCHER, label: this.lang.professional_identity.researcher },
            [PROFESSIONAL_IDENTITY.OTHER_MEDICAL]: { value: PROFESSIONAL_IDENTITY.OTHER_MEDICAL, label: this.lang.professional_identity.other_users_related_medical_field},
            [PROFESSIONAL_IDENTITY.ADMINISTRATOR]: { value: PROFESSIONAL_IDENTITY.ADMINISTRATOR, label: Tool.capitalizeFirstLetter(this.lang.professional_identity.administrator)  },
            [PROFESSIONAL_IDENTITY.TECHNICAL_SUPPORT]: { value: PROFESSIONAL_IDENTITY.TECHNICAL_SUPPORT, label: Tool.capitalizeFirstLetter(this.lang.professional_identity.technical_support) },
            [PROFESSIONAL_IDENTITY.OTHER_VENDOR]: { value: PROFESSIONAL_IDENTITY.OTHER_VENDOR, label: Tool.capitalizeFirstLetter(this.lang.professional_identity.other) },
            [PROFESSIONAL_IDENTITY.TEACHER]: { value: PROFESSIONAL_IDENTITY.TEACHER, label: Tool.capitalizeFirstLetter(this.lang.teacher) },
            [PROFESSIONAL_IDENTITY.STUDENT]: { value: PROFESSIONAL_IDENTITY.STUDENT, label: Tool.capitalizeFirstLetter(this.lang.student) },
            [PROFESSIONAL_IDENTITY.OTHER_MEDICAL_SCHOOL]: { value: PROFESSIONAL_IDENTITY.OTHER_MEDICAL_SCHOOL, label: Tool.capitalizeFirstLetter(this.lang.professional_identity.other_medical_school) }
        }
        this.formRules = {
            application_area: [
                { required: true, message: this.lang.input_select_tips, trigger: 'change' }
            ],
            professional_identity: [
                { required: true, message: this.lang.input_select_tips, trigger: 'change' }
            ],
            professional_identity_remark: [
                { required: true, message: this.lang.please_enter_detailed_professional_identity, trigger: 'blur' }
            ],
            organization: [
                {
                    required: true,
                    message: this.lang.input_enter_tips,
                    trigger: 'blur',
                    validator: (_, value, callback) => {
                        // 如果表单被禁用（选择了"其他"应用领域），则不验证
                        if (this.isFormDisabled) {
                            callback();
                            return;
                        }
                        // 否则进行必填验证
                        if (!value || value.trim() === '') {
                            callback(new Error(this.lang.input_enter_tips));
                        } else {
                            callback();
                        }
                    }
                }
            ]
        }
        this.applicationAreaOptions = [
            { value: APPLICATION_AREA.MEDICAL_INSTITUTION, label: Tool.capitalizeFirstLetter(this.lang.medical_institution) },
            { value: APPLICATION_AREA.MEDICAL_SCHOOL, label: Tool.capitalizeFirstLetter(this.lang.medical_school) },
            { value: APPLICATION_AREA.VENDOR_PERSONNEL, label: Tool.capitalizeFirstLetter(this.lang.vendor_staff)},
            { value: APPLICATION_AREA.OTHER, label: Tool.capitalizeFirstLetter(this.lang.professional_identity.other) }
        ]
    },
    methods: {
        // 阻止用户关闭弹窗
        handleClickDialogClose() {
            // this.$message.warning(this.lang.please_complete_the_information);
        },

        // 应用领域改变时的处理
        onApplicationAreaChange() {
            // 清空职业身份选择
            this.formData.professional_identity = null;
            this.formData.professional_identity_remark = '';

            // 如果选择了"其他"，清空机构信息；否则恢复默认值
            if (this.formData.application_area === APPLICATION_AREA.OTHER) {
                this.formData.organization = '';
            } else {
                // 恢复默认的机构信息
                this.formData.organization = this.user.organizationName || '';
            }

            // 清除表单验证错误信息
            this.$nextTick(() => {
                if (this.$refs.form) {
                    this.$refs.form.clearValidate();
                }
            });
        },

        // 职业身份改变时的处理
        onProfessionalIdentityChange() {
            // 如果不是需要备注的选项，清空备注
            if (this.formData.professional_identity !== PROFESSIONAL_IDENTITY.OTHER_MEDICAL &&
                this.formData.professional_identity !== PROFESSIONAL_IDENTITY.OTHER_MEDICAL_SCHOOL) {
                this.formData.professional_identity_remark = '';
            }
        },

        submit() {
            // 如果选择了"其他"应用领域，不允许提交
            if (this.formData.application_area === APPLICATION_AREA.OTHER) {
                return;
            }

            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.setProfessionalIdentity();
                } else {
                    // this.$message.warning('请完善必填信息');
                }
            });
        },

        setProfessionalIdentity() {
            // 构建提交数据
            const submitData = {
                application_area: this.formData.application_area,
                professional_identity: this.formData.professional_identity,
                organization: this.formData.organization,
                professional_identity_remark: ''
            };

            // 如果选择了需要备注的选项，添加备注
            if (this.formData.professional_identity === PROFESSIONAL_IDENTITY.OTHER_MEDICAL ||
                this.formData.professional_identity === PROFESSIONAL_IDENTITY.OTHER_MEDICAL_SCHOOL) {
                submitData.professional_identity_remark = this.formData.professional_identity_remark;
            }

            service.setProfessionalIdentity(submitData).then((res) => {
                console.log("setProfessionalIdentity:", res);
                if (res.data && res.data.error_code === 0) {
                    // 更新用户信息
                    this.$store.commit('user/updateUser', submitData);
                    this.visible = false;
                    this.$emit('success');
                    this.$message.success(this.lang.operate_success);
                } else {
                    this.$message.error(this.lang.operate_err);
                }
            }).catch((error) => {
                console.error('设置职业身份失败:', error);
                this.$message.error(this.lang.operate_err);
            });
        },
        handleClickDialog() {
            console.log("handleClickDialog");
        }
    }
};
</script>

<style lang="scss" scoped>
.professional_identity_dialog {
    .dialog-content {
        padding: 20px;

        .vertical-form {
            :deep(.el-form-item) {
                margin-bottom: 20px;

                .el-form-item__label {
                    padding-bottom: 8px;
                    font-weight: 500;
                    color: #333;
                    line-height: 1.4;
                }

                .el-form-item__content {
                    line-height: 1.4;
                }
            }
        }

        .other-area-tip {
            margin-top: 20px;
            padding: 12px;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            color: #856404;
            font-size: 14px;
            line-height: 1.5;
        }
    }
}
</style>
