<template>
    <div>
        <CommonDialog
            :title="lang.improve_information"
            :show.sync="visible"
            width="500px"
            height="auto"
            @submit="submit"
            @handleClickDialogClose="handleClickDialogClose"
            @opened="onDialogOpened"
            :submitText="lang.confirm_txt"
            :closeOnClickModal="false"
            :closeOnPressEscape="false"
            :footShow="true"
            :headerShow="true"
            :showCloseButton="false"
            :disabledSubmit="isFormDisabled"
            append-to-body
            :modal="true"
            :z-index="3000"
            class="professional_identity_dialog"
        >
            <div class="dialog-content">
                <el-form
                    ref="form"
                    :model="formData"
                    :rules="formRules"
                    label-width="auto"
                    label-position="top"
                    size="medium"
                    class="vertical-form"
                >
                    <!-- 应用领域 -->
                    <el-form-item :label="lang.application_area" prop="application_area" required>
                        <el-select
                            ref="applicationAreaSelect"
                            v-model="formData.application_area"
                            :placeholder="lang.input_select_tips"
                            style="width: 100%"
                            @change="onApplicationAreaChange"
                            @focus="onInputFocus"
                            @click="onInputClick"
                        >
                            <el-option
                                v-for="item in applicationAreaOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <!-- 职业身份 -->
                    <el-form-item
                        :label="lang.professional_identity_title"
                        :prop="isProfessionalIdentityDisabled ? '' : 'professional_identity'"
                        :required="!isProfessionalIdentityDisabled"
                    >
                        <el-select
                            v-model="formData.professional_identity"
                            :placeholder="lang.input_select_tips"
                            style="width: 100%"
                            :disabled="isProfessionalIdentityDisabled"
                            @change="onProfessionalIdentityChange"
                        >
                            <el-option
                                v-for="item in currentIdentityOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>

                    <!-- 备注 - 仅在选择"其他医疗领域相关用户"时显示 -->
                    <el-form-item
                        v-if="showRemarkField"
                        :label="lang.remark_text"
                        prop="professional_identity_remark"
                        required
                    >
                        <el-input
                            ref="remarkInput"
                            v-model="formData.professional_identity_remark"
                            :placeholder="lang.please_enter_detailed_professional_identity"
                            style="width: 100%"
                            @focus="onInputFocus"
                            @click="onInputClick"
                        />
                    </el-form-item>

                    <!-- 机构信息 -->
                    <el-form-item
                        :label="lang.organization_name"
                        prop="organization"
                        :required="!isFormDisabled"
                    >
                        <el-input
                            ref="organizationInput"
                            v-model="formData.organization"
                            :placeholder="lang.input_enter_tips"
                            style="width: 100%"
                            :disabled="isFormDisabled"
                            @focus="onInputFocus"
                            @click="onInputClick"
                        />
                    </el-form-item>

                    <!-- 其他应用领域的提示信息 -->
                    <div v-if="showOtherAreaTip" class="other-area-tip">
                        {{ lang.other_application_area_tip }}
                    </div>
                </el-form>
            </div>
        </CommonDialog>
    </div>
</template>

<script>
import base from "../lib/base";
import CommonDialog from "../MRComponents/commonDialog.vue";
import { PROFESSIONAL_IDENTITY, APPLICATION_AREA, PROFESSIONAL_IDENTITY_BY_APPLICATION_AREA } from "../lib/constants.js";
import service from "../service/service";
import Tool from "@/common/tool";

export default {
    mixins: [base],
    name: "ProfessionalIdentityDialog",
    components: {
        CommonDialog
    },
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            visible: this.show,
            formData: {
                application_area: null,
                professional_identity: null,
                professional_identity_remark: '',
                organization:''
            },
            applicationAreaOptions: [

            ],
            allIdentityOptions: {

            },
            formRules: {

            }
        };
    },
    computed: {
        // 当前可选的职业身份选项
        currentIdentityOptions() {
            if (!this.formData.application_area) {
                return [];
            }

            const availableIdentities = PROFESSIONAL_IDENTITY_BY_APPLICATION_AREA[this.formData.application_area] || [];
            return availableIdentities.map(identityId => this.allIdentityOptions[identityId]);
        },

        // 是否显示备注字段
        showRemarkField() {
            return this.formData.professional_identity === PROFESSIONAL_IDENTITY.OTHER_MEDICAL ||
                   this.formData.professional_identity === PROFESSIONAL_IDENTITY.OTHER_MEDICAL_SCHOOL;
        },

        // 是否显示"其他"应用领域的提示
        showOtherAreaTip() {
            return this.formData.application_area === APPLICATION_AREA.OTHER;
        },

        // 表单是否禁用
        isFormDisabled() {
            return this.formData.application_area === APPLICATION_AREA.OTHER;
        },

        // 职业身份选择框是否禁用
        isProfessionalIdentityDisabled() {
            return this.isFormDisabled || !this.formData.application_area;
        }
    },
    watch: {
        show(newVal) {
            this.visible = newVal;
            if (newVal) {
                this.formData.organization = this.user.organizationName || '';
                // 确保弹窗打开后能正常获得焦点
                this.$nextTick(() => {
                    this.ensureDialogFocus();
                });
            }
        },
        visible(newVal) {
            this.$emit('update:show', newVal);
            if (newVal) {
                // 弹窗打开时确保焦点管理
                this.$nextTick(() => {
                    this.ensureDialogFocus();
                });
            }
        },
        // 监听路由变化
        '$route'() {
            // 如果路由name不再是index，则自动关闭弹窗
            if (this.visible) {
                this.visible = false;
            }
        }
    },
    created() {
        this.allIdentityOptions = {
            [PROFESSIONAL_IDENTITY.PHYSICIAN]: { value: PROFESSIONAL_IDENTITY.PHYSICIAN, label: this.lang.professional_identity.physician },
            [PROFESSIONAL_IDENTITY.SONOGRAPHER]: { value: PROFESSIONAL_IDENTITY.SONOGRAPHER, label: this.lang.professional_identity.sonographer },
            [PROFESSIONAL_IDENTITY.RESEARCHER]: { value: PROFESSIONAL_IDENTITY.RESEARCHER, label: this.lang.professional_identity.researcher },
            [PROFESSIONAL_IDENTITY.OTHER_MEDICAL]: { value: PROFESSIONAL_IDENTITY.OTHER_MEDICAL, label: this.lang.professional_identity.other_users_related_medical_field},
            [PROFESSIONAL_IDENTITY.ADMINISTRATOR]: { value: PROFESSIONAL_IDENTITY.ADMINISTRATOR, label: Tool.capitalizeFirstLetter(this.lang.professional_identity.administrator)  },
            [PROFESSIONAL_IDENTITY.TECHNICAL_SUPPORT]: { value: PROFESSIONAL_IDENTITY.TECHNICAL_SUPPORT, label: Tool.capitalizeFirstLetter(this.lang.professional_identity.technical_support) },
            [PROFESSIONAL_IDENTITY.OTHER_VENDOR]: { value: PROFESSIONAL_IDENTITY.OTHER_VENDOR, label: Tool.capitalizeFirstLetter(this.lang.professional_identity.other) },
            [PROFESSIONAL_IDENTITY.TEACHER]: { value: PROFESSIONAL_IDENTITY.TEACHER, label: Tool.capitalizeFirstLetter(this.lang.teacher) },
            [PROFESSIONAL_IDENTITY.STUDENT]: { value: PROFESSIONAL_IDENTITY.STUDENT, label: Tool.capitalizeFirstLetter(this.lang.student) },
            [PROFESSIONAL_IDENTITY.OTHER_MEDICAL_SCHOOL]: { value: PROFESSIONAL_IDENTITY.OTHER_MEDICAL_SCHOOL, label: Tool.capitalizeFirstLetter(this.lang.professional_identity.other_medical_school) }
        }
        this.formRules = {
            application_area: [
                { required: true, message: this.lang.input_select_tips, trigger: 'change' }
            ],
            professional_identity: [
                { required: true, message: this.lang.input_select_tips, trigger: 'change' }
            ],
            professional_identity_remark: [
                { required: true, message: this.lang.please_enter_detailed_professional_identity, trigger: 'blur' }
            ],
            organization: [
                {
                    required: true,
                    message: this.lang.input_enter_tips,
                    trigger: 'blur',
                    validator: (_, value, callback) => {
                        // 如果表单被禁用（选择了"其他"应用领域），则不验证
                        if (this.isFormDisabled) {
                            callback();
                            return;
                        }
                        // 否则进行必填验证
                        if (!value || value.trim() === '') {
                            callback(new Error(this.lang.input_enter_tips));
                        } else {
                            callback();
                        }
                    }
                }
            ]
        }
        this.applicationAreaOptions = [
            { value: APPLICATION_AREA.MEDICAL_INSTITUTION, label: Tool.capitalizeFirstLetter(this.lang.medical_institution) },
            { value: APPLICATION_AREA.MEDICAL_SCHOOL, label: Tool.capitalizeFirstLetter(this.lang.medical_school) },
            { value: APPLICATION_AREA.VENDOR_PERSONNEL, label: Tool.capitalizeFirstLetter(this.lang.vendor_staff)},
            { value: APPLICATION_AREA.OTHER, label: Tool.capitalizeFirstLetter(this.lang.professional_identity.other) }
        ]
    },
    methods: {
        // 阻止用户关闭弹窗
        handleClickDialogClose() {
            // this.$message.warning(this.lang.please_complete_the_information);
        },

        // 应用领域改变时的处理
        onApplicationAreaChange() {
            // 清空职业身份选择
            this.formData.professional_identity = null;
            this.formData.professional_identity_remark = '';

            // 如果选择了"其他"，清空机构信息；否则恢复默认值
            if (this.formData.application_area === APPLICATION_AREA.OTHER) {
                this.formData.organization = '';
            } else {
                // 恢复默认的机构信息
                this.formData.organization = this.user.organizationName || '';
            }

            // 清除表单验证错误信息
            this.$nextTick(() => {
                if (this.$refs.form) {
                    this.$refs.form.clearValidate();
                }
            });
        },

        // 职业身份改变时的处理
        onProfessionalIdentityChange() {
            // 如果不是需要备注的选项，清空备注
            if (this.formData.professional_identity !== PROFESSIONAL_IDENTITY.OTHER_MEDICAL &&
                this.formData.professional_identity !== PROFESSIONAL_IDENTITY.OTHER_MEDICAL_SCHOOL) {
                this.formData.professional_identity_remark = '';
            }
        },

        submit() {
            // 如果选择了"其他"应用领域，不允许提交
            if (this.formData.application_area === APPLICATION_AREA.OTHER) {
                return;
            }

            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.setProfessionalIdentity();
                } else {
                    // this.$message.warning('请完善必填信息');
                }
            });
        },

        setProfessionalIdentity() {
            // 构建提交数据
            const submitData = {
                application_area: this.formData.application_area,
                professional_identity: this.formData.professional_identity,
                organization: this.formData.organization,
                professional_identity_remark: ''
            };

            // 如果选择了需要备注的选项，添加备注
            if (this.formData.professional_identity === PROFESSIONAL_IDENTITY.OTHER_MEDICAL ||
                this.formData.professional_identity === PROFESSIONAL_IDENTITY.OTHER_MEDICAL_SCHOOL) {
                submitData.professional_identity_remark = this.formData.professional_identity_remark;
            }

            service.setProfessionalIdentity(submitData).then((res) => {
                console.log("setProfessionalIdentity:", res);
                if (res.data && res.data.error_code === 0) {
                    // 更新用户信息
                    this.$store.commit('user/updateUser', submitData);
                    this.visible = false;
                    this.$emit('success');
                    this.$message.success(this.lang.operate_success);
                } else {
                    this.$message.error(this.lang.operate_err);
                }
            }).catch((error) => {
                console.error('设置职业身份失败:', error);
                this.$message.error(this.lang.operate_err);
            });
        },

        // 确保弹窗能正常获得焦点
        ensureDialogFocus() {
            try {
                // 延迟执行，确保DOM完全渲染
                setTimeout(() => {
                    // 找到当前弹窗的DOM元素
                    const dialogWrapper = document.querySelector('.professional_identity_dialog .el-dialog__wrapper');
                    if (dialogWrapper) {
                        // 设置更高的z-index
                        dialogWrapper.style.zIndex = '3000';

                        // 找到弹窗内容区域
                        const dialog = dialogWrapper.querySelector('.el-dialog');
                        if (dialog) {
                            dialog.style.zIndex = '3001';

                            // 移除可能阻止焦点的属性
                            dialog.removeAttribute('tabindex');

                            // 确保所有输入框都有正确的z-index和焦点属性
                            const inputs = dialog.querySelectorAll('.el-input__inner, .el-textarea__inner');
                            inputs.forEach((input) => {
                                input.style.position = 'relative';
                                input.style.zIndex = '3002';
                                input.style.pointerEvents = 'auto';
                                input.removeAttribute('readonly');
                                input.removeAttribute('disabled');
                            });

                            // 确保下拉框也有正确的z-index
                            const selects = dialog.querySelectorAll('.el-select');
                            selects.forEach((select) => {
                                select.style.position = 'relative';
                                select.style.zIndex = '3002';
                                select.style.pointerEvents = 'auto';
                            });

                            // 强制移除所有可能阻止事件的遮罩
                            const masks = document.querySelectorAll('.v-modal');
                            masks.forEach((mask, index) => {
                                if (index < masks.length - 1) { // 保留最后一个遮罩
                                    mask.style.pointerEvents = 'none';
                                }
                            });
                        }
                    }
                }, 100);
            } catch (error) {
                console.warn('设置弹窗焦点时出错:', error);
            }
        },

        // 弹窗完全打开后的处理
        onDialogOpened() {
            console.log('弹窗已打开，开始处理焦点问题');
            this.ensureDialogFocus();

            // 额外的焦点修复措施
            this.$nextTick(() => {
                // 尝试直接修复Element UI的焦点陷阱问题
                this.fixElementUIFocusTrap();
            });
        },

        // 修复Element UI的焦点陷阱问题
        fixElementUIFocusTrap() {
            try {
                // 找到所有的弹窗包装器
                const allDialogWrappers = document.querySelectorAll('.el-dialog__wrapper');

                // 只对最后一个（最顶层的）弹窗启用焦点陷阱
                allDialogWrappers.forEach((wrapper, index) => {
                    const dialog = wrapper.querySelector('.el-dialog');
                    if (dialog) {
                        if (index === allDialogWrappers.length - 1) {
                            // 最顶层弹窗：确保可以获得焦点
                            dialog.removeAttribute('tabindex');
                            dialog.style.outline = 'none';

                            // 确保表单元素可以获得焦点
                            const formElements = dialog.querySelectorAll('input, textarea, select, button');
                            formElements.forEach(element => {
                                element.removeAttribute('tabindex');
                                element.style.pointerEvents = 'auto';
                            });
                        } else {
                            // 底层弹窗：禁用焦点陷阱
                            dialog.setAttribute('tabindex', '-1');
                            const formElements = dialog.querySelectorAll('input, textarea, select, button');
                            formElements.forEach(element => {
                                element.setAttribute('tabindex', '-1');
                            });
                        }
                    }
                });

                console.log('焦点陷阱修复完成');
            } catch (error) {
                console.warn('修复焦点陷阱时出错:', error);
            }
        },

        // 输入框获得焦点时的处理
        onInputFocus(event) {
            console.log('输入框获得焦点:', event.target);
            // 确保当前输入框在最顶层
            if (event.target) {
                event.target.style.position = 'relative';
                event.target.style.zIndex = '3010';
                event.target.style.pointerEvents = 'auto';
            }
        },

        // 输入框点击时的处理
        onInputClick(event) {
            console.log('输入框被点击:', event.target);
            // 阻止事件冒泡，防止被底层弹窗拦截
            event.stopPropagation();

            // 强制聚焦
            if (event.target && typeof event.target.focus === 'function') {
                setTimeout(() => {
                    event.target.focus();
                }, 10);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.professional_identity_dialog {
    // 确保弹窗在最顶层
    :deep(.el-dialog__wrapper) {
        z-index: 3000 !important;
    }

    :deep(.el-dialog) {
        z-index: 3001 !important;
    }

    // 确保表单元素可以正常获得焦点
    :deep(.el-input__inner),
    :deep(.el-textarea__inner),
    :deep(.el-select .el-input__inner) {
        position: relative;
        z-index: 3002 !important;
    }

    .dialog-content {
        padding: 20px;

        .vertical-form {
            :deep(.el-form-item) {
                margin-bottom: 20px;

                .el-form-item__label {
                    padding-bottom: 8px;
                    font-weight: 500;
                    color: #333;
                    line-height: 1.4;
                }

                .el-form-item__content {
                    line-height: 1.4;
                }
            }
        }

        .other-area-tip {
            margin-top: 20px;
            padding: 12px;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            color: #856404;
            font-size: 14px;
            line-height: 1.5;
        }
    }
}
</style>
